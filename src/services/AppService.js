const config = require('../config/config');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');
const { sequelize } = require('../models');

class AppService {
  constructor() {
    this.config = config;
  }

  assert(condition, message) {
    if (!condition) {
      throw new InvalidError(message);
    }
  }

  exists(object, message) {
    if (!object) {
      throw new NotFoundError(message);
    }
  }

  async transaction(callback) {
    // Check if there's already an active transaction (e.g., in tests)
    const existingTransaction = sequelize.constructor._cls?.get('transaction');

    if (existingTransaction) {
      // Use the existing transaction
      return await callback(existingTransaction);
    }

    // Create a new transaction if none exists
    const transaction = await sequelize.transaction();
    try {
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

module.exports = AppService;
